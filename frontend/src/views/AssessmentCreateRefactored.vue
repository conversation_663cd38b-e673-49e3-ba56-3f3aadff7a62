<template>
  <PhantomLayout title="Create Assessment">
    <div class="w-full p-7 -mt-14">
      <!-- Back Button -->
      <div class="flex justify-end mb-8">
        <button
          class="btn-phantom-secondary px-6 py-3 text-base"
          @click="navigateBack"
        >
          <span class="flex items-center">
            <SvgIcon name="arrow-left" class="mr-2" />
            Back to Assessment List
          </span>
        </button>
      </div>

      <form class="space-y-12" @submit.prevent="handleSubmit">
        <!-- Basic Information and Skills Selection -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Basic Information -->
          <AssessmentBasicInfo
            :form-data="formData"
            :errors="errors"
            @update:form-data="updateFormData"
          />

          <!-- Skills and Question Mode -->
          <div class="space-y-8">
            <!-- Skills Selection -->
            <AssessmentSkillsSelection
              v-model:selected-skill-ids="formData.selectedSkillIds"
              :skills="skills"
              :skills-loading="skillsLoading"
              :has-interacted-with-skills="hasInteractedWithSkills"
              :errors="errors"
              @skill-interaction="handleSkillInteraction"
            />

            <!-- Question Selection Mode -->
            <AssessmentQuestionMode
              v-model:question-selection-mode="formData.questionSelectionMode"
            />
          </div>
        </div>

        <!-- Fixed Questions Section -->
        <AssessmentFixedQuestions
          v-if="formData.questionSelectionMode === 'fixed' && formData.selectedSkillIds.length > 0"
          :selected-skill-ids="formData.selectedSkillIds"
          :created-assessment="createdAssessment"
        />

        <!-- Status Messages -->
        <StatusMessages
          :is-loading="isLoading"
          :loading-message="loadingMessage"
          :message="message"
          :is-success="isSuccess"
        />

        <!-- Success Details -->
        <AssessmentSuccessDetails
          v-if="createdAssessment"
          :assessment="createdAssessment"
        />

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4">
          <button
            type="button"
            class="btn-phantom-secondary px-6 py-3"
            @click="resetForm"
          >
            Reset Form
          </button>
          <button
            type="submit"
            class="btn-phantom px-6 py-3"
            :disabled="isLoading || !isFormValid"
          >
            <span v-if="isLoading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Creating...
            </span>
            <span v-else>Create Assessment</span>
          </button>
        </div>
      </form>
    </div>
  </PhantomLayout>
</template>

<script setup>
/**
 * Refactored Assessment Creation Component
 * 
 * This component has been broken down into smaller, manageable parts:
 * - Uses composables for business logic
 * - Splits UI into focused components
 * - Reduces complexity and improves maintainability
 */
import { ref, onMounted, watch } from 'vue';
import { useNavigation, useAssessmentForm } from '@/composables';
import { logUserAction } from '@/utils/logger';

// Layout and UI Components
import PhantomLayout from '@/components/layout/Layout.vue';
import SvgIcon from '@/components/SvgIcon.vue';

// Assessment-specific Components
import AssessmentBasicInfo from '@/components/assessments/AssessmentBasicInfo.vue';
import AssessmentSkillsSelection from '@/components/assessments/AssessmentSkillsSelection.vue';
import AssessmentQuestionMode from '@/components/assessments/AssessmentQuestionMode.vue';
import AssessmentFixedQuestions from '@/components/assessments/AssessmentFixedQuestions.vue';
import AssessmentSuccessDetails from '@/components/assessments/AssessmentSuccessDetails.vue';
import StatusMessages from '@/components/ui/StatusMessages.vue';

// Composables
const navigation = useNavigation();
const assessmentForm = useAssessmentForm();

// Local state
const hasInteractedWithSkills = ref(false);
const createdAssessment = ref(null);

// Destructure composable state and methods
const {
  formData,
  skills,
  skillsLoading,
  errors,
  isLoading,
  loadingMessage,
  message,
  isSuccess,
  isFormValid,
  fetchSkills,
  submitAssessment,
  resetForm: resetAssessmentForm,
  clearMessage
} = assessmentForm;

// Event handlers
const handleSubmit = async () => {
  try {
    logUserAction('assessment_creation_attempted', {
      name: formData.value.name,
      skillCount: formData.value.selectedSkillIds.length,
      mode: formData.value.questionSelectionMode
    });

    const result = await submitAssessment();
    if (result) {
      createdAssessment.value = result;
    }
  } catch (error) {
    // Error handling is done in the composable
    console.error('Assessment creation failed:', error);
  }
};

const handleSkillInteraction = () => {
  hasInteractedWithSkills.value = true;
  clearMessage();
};

const updateFormData = (newData) => {
  Object.assign(formData.value, newData);
};

const navigateBack = () => {
  navigation.navigateTo('/list-assessments');
};

const resetForm = () => {
  resetAssessmentForm();
  hasInteractedWithSkills.value = false;
  createdAssessment.value = null;
};

// Lifecycle
onMounted(async () => {
  try {
    await fetchSkills();
    logUserAction('assessment_create_page_loaded');
  } catch (error) {
    console.error('Failed to load skills:', error);
  }
});

// Watchers
watch(
  () => formData.value.selectedSkillIds,
  (newSkills) => {
    if (newSkills.length === 0) {
      createdAssessment.value = null;
    }
  },
  { deep: true }
);
</script>

<style scoped>
/* Component-specific styles */
.btn-phantom {
  @apply bg-gradient-to-r from-phantom-blue to-phantom-indigo text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-phantom-blue/25 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg transition-all duration-200 hover:bg-white/10 hover:border-white/20;
}
</style>