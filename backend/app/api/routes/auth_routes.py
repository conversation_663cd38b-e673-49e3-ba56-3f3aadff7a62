import os
from typing import Optional

import requests
from authlib.integrations.httpx_client import Async<PERSON><PERSON>2<PERSON>lient
from authlib.integrations.starlette_client import OAuth
from authlib.jose import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from dotenv import load_dotenv
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel

from ...utils.logger import error as log_error
from ...utils.logger import info as log_info
from ...utils.logger import (
    log_auth_event,
)
from ...utils.logger import warning as log_warning

# Load environment variables
load_dotenv()

router = APIRouter(prefix="/auth", tags=["Authentication"])


# --- Token request model ---
class TokenRequest(BaseModel):
    code: str
    redirect_uri: str
    state: Optional[str] = None


# --- OAuth2 Client Setup ---
oauth = OAuth()
oauth_config = {
    "name": "dex",
    "client_id": os.getenv("AUTH_CLIENT_ID"),
    "client_secret": os.getenv("AUTH_CLIENT_SECRET"),
    "access_token_url": os.getenv("AUTH_TOKEN_URL"),
    "authorize_url": os.getenv("AUTH_AUTHORIZE_URL"),
    "client_kwargs": {"scope": "openid email profile groups"},
}
oauth.register(**oauth_config)

DEX_ISSUER = os.getenv("AUTH_ISSUER", "")
JWKS_URL = os.getenv("AUTH_JWKS_URI", f"{DEX_ISSUER}/keys")


# --- JWT Parser ---
def parse_dex_id_token(id_token: str) -> dict:
    try:
        jwks = JsonWebKey.import_key_set(requests.get(JWKS_URL).json())
        claims = jwt.decode(id_token, key=jwks)
        claims.validate()  # Only checks exp, nbf, etc.

        # Manual issuer validation
        if claims.get("iss") != DEX_ISSUER:
            raise JoseError(f"Issuer mismatch: {claims.get('iss')} != {DEX_ISSUER}")

        log_auth_event("token_validation", success=True, user_id=claims.get("sub"))
        log_info(f"ID Token claims received: {dict(claims)}")  # Debug logging
        return dict(claims)
    except JoseError as e:
        log_auth_event("token_validation", success=False, error=str(e))
        raise HTTPException(status_code=401, detail="Invalid ID token")


# === OAuth Login Redirect ===
@router.get("/login")
async def login(request: Request):
    redirect_uri = os.getenv("AUTH_REDIRECT_URI")
    return await oauth.dex.authorize_redirect(request, redirect_uri)


# === Callback After Dex Login ===
@router.get("/callback")
async def callback(request: Request):
    try:
        token = await oauth.dex.authorize_access_token(request)
        user = await oauth.dex.parse_id_token(request, token)

        # Check if we have valid user information
        if not user:
            log_auth_event(
                "callback", success=False, error="No valid user information found"
            )
            return RedirectResponse(
                url=f"{os.getenv('FRONTEND_URL')}/login?error=no_user_info"
            )

        log_auth_event("callback", success=True, user_id=user.get("sub"))
        log_info(f"OAuth callback user data: {dict(user)}")  # Debug logging
        log_info(f"User email field: {user.get('email')}")  # Debug email specifically
        log_info(
            f"User mail field: {user.get('mail')}"
        )  # Debug mail field specifically

        # Get groups from user data, or assign default groups based on user
        user_groups = user.get("groups", [])

        # Temporary workaround: assign groups based on username if not present
        if not user_groups:
            username = user.get("name", "").lower()
            # Based on LDAP configuration, assign groups
            if username in ["john", "jane", "rakshit"]:
                user_groups = ["admins"]
            elif username in ["rakshit", "rahul", "priyanshee", "pratiksha", "kunjan"]:
                user_groups = ["employees"]
            else:
                user_groups = ["employees"]  # Default to employees
            log_info(f"Assigned default groups for user {username}: {user_groups}")

        # Filter out sensitive data before storing in session, keep groups for authorization
        safe_user_info = {
            "sub": user.get("sub"),
            "name": user.get("name", "User"),
            "email": user.get(
                "email", user.get("mail")
            ),  # Include email for header display
            "groups": user_groups,  # Keep groups for authorization
        }
        log_info(f"Safe user info being stored: {safe_user_info}")  # Debug logging
        request.session["user"] = safe_user_info
        return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/")
    except Exception as e:
        log_auth_event("callback", success=False, error=str(e))
        return RedirectResponse(
            url=f"{os.getenv('FRONTEND_URL')}/login?error=authentication_failed"
        )


# === Check if User is Logged In ===
@router.get("/userinfo")
async def userinfo(request: Request):
    user = request.session.get("user")
    log_info(f"Session user data: {user}")  # Debug logging
    if user:
        # Return user information needed for frontend authorization
        safe_user_info = {
            "name": user.get("name", "User"),
            "email": user.get("email"),  # Keep email for header display
            "groups": user.get(
                "groups", []
            ),  # Include groups for authorization decisions
            # Remove sensitive data like sub, access tokens, etc.
        }
        log_info(f"Returning user info with groups: {safe_user_info}")  # Debug logging
        return {"authenticated": True, "user": safe_user_info}
    return {"authenticated": False, "user": None}


# === Logout ===
@router.get("/logout")
async def logout(request: Request):
    # Clear the user session
    request.session.pop("user", None)

    # Check if this is an API call (has Accept: application/json header)
    accept_header = request.headers.get("accept", "")
    if "application/json" in accept_header:
        # Return JSON response for API calls
        return {"success": True, "message": "Logged out successfully"}
    else:
        # Redirect to Dex logout for browser navigation
        return RedirectResponse(url=os.getenv("AUTH_LOGOUT_URL"))


# === Code-to-Token Exchange (for frontend) ===
@router.post("/token")
async def exchange_token(request_data: TokenRequest, request: Request):
    try:
        log_info(
            f"Token exchange request received - code: {request_data.code[:10]}..., "
            f"redirect_uri: {request_data.redirect_uri}"
        )

        client = AsyncOAuth2Client(
            client_id=os.getenv("AUTH_CLIENT_ID"),
            client_secret=os.getenv("AUTH_CLIENT_SECRET"),
            token_endpoint=os.getenv("AUTH_TOKEN_URL"),
        )

        log_info(f"Attempting token exchange with Dex at {os.getenv('AUTH_TOKEN_URL')}")

        token = await client.fetch_token(
            url=os.getenv("AUTH_TOKEN_URL"),
            code=request_data.code,
            redirect_uri=request_data.redirect_uri,
        )

        log_info("Token exchange with Dex successful")
        id_token = token.get("id_token")
        user_info = {}

        if id_token:
            try:
                parsed_user_info = parse_dex_id_token(id_token)

                # Get groups from parsed data, or assign default groups
                user_groups = parsed_user_info.get("groups", [])

                # Temporary workaround: assign groups based on username if not present
                if not user_groups:
                    username = parsed_user_info.get("name", "").lower()
                    # Based on LDAP configuration, assign groups
                    if username in ["john", "jane", "rakshit"]:
                        user_groups = ["admins"]
                    elif username in [
                        "rakshit",
                        "rahul",
                        "priyanshee",
                        "pratiksha",
                        "kunjan",
                    ]:
                        user_groups = ["employees"]
                    else:
                        user_groups = ["employees"]  # Default to employees
                    log_info(
                        f"Assigned default groups for user {username}: {user_groups}"
                    )

                # Filter out sensitive data before storing in session
                user_info = {
                    "sub": parsed_user_info.get("sub"),
                    "name": parsed_user_info.get("name", "User"),
                    "email": parsed_user_info.get(
                        "email", parsed_user_info.get("mail")
                    ),  # Include email for header display
                    "groups": user_groups,  # Keep groups for authorization
                }
                log_info("Successfully parsed user info from ID token")
            except Exception as e:
                log_warning(f"Failed to parse ID token: {e}")

        # If we couldn't get user info from the ID token and there's no fallback info in the token
        if not user_info and not (
            token.get("sub") or token.get("email") or token.get("name")
        ):
            log_error("No valid user information found in token")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed: No valid user information found",
            )

        # Only use fallback if we have at least some basic info in the token
        if not user_info:
            fallback_groups = token.get("groups", [])

            # Assign default groups if not present
            if not fallback_groups:
                username = token.get("name", "").lower()
                if username in ["john", "jane", "rakshit"]:
                    fallback_groups = ["admins"]
                elif username in [
                    "rakshit",
                    "rahul",
                    "priyanshee",
                    "pratiksha",
                    "kunjan",
                ]:
                    fallback_groups = ["employees"]
                else:
                    fallback_groups = ["employees"]  # Default to employees
                log_info(
                    f"Assigned default groups for fallback user {username}: {fallback_groups}"
                )

            user_info = {
                "sub": token.get("sub", "user"),
                "name": token.get("name", "Authenticated User"),
                "email": token.get(
                    "email", token.get("mail")
                ),  # Include email for header display
                "groups": fallback_groups,  # Keep groups for authorization
            }
            log_warning("Using fallback user info from token")

        request.session["user"] = user_info

        # Return only success confirmation - sensitive data is stored in session
        return {
            "success": True,
            "message": "Authentication successful",
            "authenticated": True,
        }

    except Exception as e:
        log_error(f"Error exchanging token: {str(e)}", exception=e)
        # Provide more specific error details
        if "invalid_grant" in str(e):
            raise HTTPException(
                status_code=400, detail="Invalid or expired authorization code"
            )
        elif "invalid_client" in str(e):
            raise HTTPException(status_code=400, detail="Invalid client credentials")
        else:
            raise HTTPException(
                status_code=400, detail=f"Token exchange failed: {str(e)}"
            )


# === Setup Function for App ===
def setup_auth(app):
    app.include_router(router)
