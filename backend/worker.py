"""
Herbit Worker Service - Minimal Version
Only processes question generation tasks from Dapr pubsub.
Uses PostgreSQL ONLY for storing generated questions (business data).
Uses Dapr for ALL operational data (task status, DLQ, etc.).
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Optional

import httpx
from app.services.create_quiz_questions import ask_for_question
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel

# Set up logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Herbit Worker Service",
    description="Processes question generation tasks via Dapr",
    version="1.0.0",
)

# ============================================================================
# MODELS
# ============================================================================


class QuestionGenerationTask(BaseModel):
    task_id: str
    skill_id: int
    skill_name: str
    skill_description: str
    created_at: Optional[str] = None


# ============================================================================
# STARTUP
# ============================================================================


@app.on_event("startup")
async def startup_event():
    """Initialize worker service"""
    logger.info("Worker service starting...")

    # Give Dapr sidecar a moment to start
    await asyncio.sleep(2.0)

    # Wait for Dapr sidecar to be ready
    if await wait_for_dapr_ready():
        logger.info("Worker service started - processing tasks via Dapr")
    else:
        logger.warning("Worker service started but Dapr sidecar is not ready")


# ============================================================================
# CORE FUNCTIONS
# ============================================================================


async def wait_for_dapr_ready(max_retries: int = 30, delay: float = 1.0) -> bool:
    """
    Wait for Dapr sidecar to be ready.
    Returns True if Dapr is ready, False if timeout.
    """
    dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
    health_url = f"http://localhost:{dapr_port}/v1.0/healthz"

    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient(timeout=2.0) as client:
                response = await client.get(health_url)
                if response.status_code in [
                    200,
                    204,
                ]:  # Dapr health endpoint returns 204
                    if attempt > 0:  # Only log if it took multiple attempts
                        logger.info(f"Dapr sidecar ready after {attempt + 1} attempts")
                    return True
        except Exception:
            if attempt == 0:
                logger.info("Waiting for Dapr sidecar...")

        if attempt < max_retries - 1:
            await asyncio.sleep(delay)

    logger.error(f"Dapr sidecar not ready after {max_retries} attempts")
    return False


async def call_dapr_with_retry(
    method: str,
    url: str,
    json_data: Optional[dict] = None,
    max_retries: int = 3,
    base_delay: float = 1.0,
    timeout: float = 10.0,
) -> Optional[httpx.Response]:
    """
    Make HTTP calls to Dapr with retry logic and exponential backoff.
    """
    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url)
                elif method.upper() == "POST":
                    response = await client.post(url, json=json_data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Return response for both success and client errors (4xx)
                # Only retry on server errors (5xx) and connection errors
                if response.status_code < 500:
                    return response

                logger.warning(
                    f"Dapr call failed with status {response.status_code}, attempt {attempt + 1}/{max_retries}"
                )

        except (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError) as e:
            if attempt == max_retries - 1:  # Only log on final attempt
                logger.warning(f"Dapr connection error: {e}")
        except Exception as e:
            if attempt == max_retries - 1:  # Only log on final attempt
                logger.error(f"Unexpected error calling Dapr: {e}")

        if attempt < max_retries - 1:
            delay = base_delay * (2**attempt)  # Exponential backoff
            await asyncio.sleep(delay)

    logger.error(f"All {max_retries} Dapr call attempts failed for {method} {url}")
    return None


async def store_task_status_in_dapr(
    task_id: str,
    status: str,
    skill_id: int,
    skill_name: str,
    questions_generated: int = 0,
    error_message: Optional[str] = None,
):
    """Store task status in Dapr state store (operational data only)"""
    dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")

    task_data = {
        "task_id": task_id,
        "status": status,
        "skill_id": skill_id,
        "skill_name": skill_name,
        "questions_generated": questions_generated,
        "error_message": error_message,
        "timestamp": datetime.utcnow().isoformat(),
    }

    state_data = [{"key": f"task-{task_id}", "value": task_data}]
    save_url = f"http://localhost:{dapr_port}/v1.0/state/statestore"

    response = await call_dapr_with_retry("POST", save_url, json_data=state_data)

    if response and response.status_code in [200, 201, 204]:
        logger.debug(f"Task status stored in Dapr: {task_id} -> {status}")
    else:
        status_code = response.status_code if response else "No response"
        logger.error(f"Failed to store task status in Dapr: HTTP {status_code}")


async def process_question_generation(task: QuestionGenerationTask) -> tuple[bool, int]:
    """
    Process question generation for a skill.
    Returns (success, questions_generated)
    """
    start_time = time.time()

    try:
        logger.info(
            f"Starting question generation for skill: {task.skill_name} (ID: {task.skill_id})"
        )

        # Generate questions using the existing function
        # This function will store questions in PostgreSQL (business data)
        quiz_name = f"skill_{task.skill_id}_{task.skill_name}"
        topics = task.skill_description

        result = await ask_for_question(
            quiz_name, topics, skill_id=task.skill_id, skill_name=task.skill_name
        )

        processing_time = time.time() - start_time

        if result:
            # The ask_for_question function should return the number of questions generated
            # If it doesn't, we'll assume 1 question was generated
            questions_generated = result if isinstance(result, int) else 1

            logger.info(
                f"Generated {questions_generated} questions for skill: {task.skill_name} in {processing_time:.2f}s"
            )
            return True, questions_generated
        else:
            logger.error(
                f"Failed to generate questions for skill: {task.skill_name} after {processing_time:.2f}s"
            )
            return False, 0

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            f"Error in question generation for skill {task.skill_name} after {processing_time:.2f}s: {e}",
            exc_info=True,
        )
        return False, 0


# ============================================================================
# DAPR ENDPOINTS (REQUIRED FOR PUBSUB)
# ============================================================================


@app.get("/dapr/subscribe")
async def dapr_subscribe():
    """
    Dapr subscription configuration endpoint.
    This tells Dapr what topics this service is subscribed to.
    """
    subscriptions = [
        {"pubsubname": "pubsub", "topic": "question-tasks", "route": "/question-tasks"},
        {
            "pubsubname": "pubsub",
            "topic": "question-tasks-dlq",
            "route": "/dlq-handler",
        },
        {
            "pubsubname": "pubsub",
            "topic": "generate-questions",
            "route": "/generate-questions",
        },
        {
            "pubsubname": "pubsub",
            "topic": "generate-questions-dlq",
            "route": "/dlq-handler",
        },
    ]

    logger.info(f"Registering Dapr subscriptions: {subscriptions}")
    return subscriptions


@app.post("/question-tasks")
async def handle_question_task(request: Request):
    """Handle question generation tasks from Dapr pubsub"""
    try:
        body = await request.body()
        logger.info(f"Received task: {body.decode()}")

        data = json.loads(body)
        task_data = data.get("data", data)

        # Parse task
        try:
            task = QuestionGenerationTask(**task_data)
        except Exception as e:
            logger.error(f"Invalid task data format: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid task data: {str(e)}")

        # Check if task already processed (deduplication using Dapr)
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
        task_url = (
            f"http://localhost:{dapr_port}/v1.0/state/statestore/task-{task.task_id}"
        )

        response = await call_dapr_with_retry("GET", task_url)

        if response and response.status_code == 200:
            try:
                existing_task = response.json()
                if existing_task.get("status") == "completed":
                    logger.info(f"Task {task.task_id} already completed. Skipping.")
                    return  # HTTP 200 with empty body for Dapr pub/sub success
                elif existing_task.get("status") == "processing":
                    logger.info(
                        f"Task {task.task_id} is already being processed. Skipping."
                    )
                    return  # HTTP 200 with empty body for Dapr pub/sub success
            except Exception as e:
                logger.error(f"Failed to parse existing task data: {e}")
        elif response is None:
            logger.warning(
                f"Could not check task duplication for {task.task_id} - proceeding anyway"
            )

        # Mark as processing in Dapr
        await store_task_status_in_dapr(
            task.task_id, "processing", task.skill_id, task.skill_name
        )

        # Process the task with timeout
        timeout_seconds = int(
            os.getenv("QUESTION_GENERATION_TIMEOUT", "600")
        )  # 10 minutes

        try:
            success, questions_generated = await asyncio.wait_for(
                process_question_generation(task), timeout=timeout_seconds
            )

            if success:
                # Store success status in Dapr
                await store_task_status_in_dapr(
                    task.task_id,
                    "completed",
                    task.skill_id,
                    task.skill_name,
                    questions_generated,
                )

                logger.info(
                    f"Successfully generated {questions_generated} questions for skill: {task.skill_name}"
                )
                return  # HTTP 200 with empty body for Dapr pub/sub success
            else:
                # Store failure status in Dapr
                await store_task_status_in_dapr(
                    task.task_id,
                    "failed",
                    task.skill_id,
                    task.skill_name,
                    0,
                    "Question generation failed",
                )

                # Return 500 to trigger Dapr retries
                raise HTTPException(
                    status_code=500,
                    detail=f"Question generation failed for skill: {task.skill_name}",
                )

        except asyncio.TimeoutError:
            error_msg = f"Timeout after {timeout_seconds} seconds"
            logger.error(f"Task {task.task_id} timed out: {error_msg}")

            # Store timeout status in Dapr
            await store_task_status_in_dapr(
                task.task_id, "failed", task.skill_id, task.skill_name, 0, error_msg
            )

            # Return 500 to trigger retries
            raise HTTPException(
                status_code=500, detail=f"Question generation timed out: {error_msg}"
            )

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse request body: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        logger.error(f"Error processing task: {e}", exc_info=True)
        # Return 500 to trigger Dapr retries
        raise HTTPException(status_code=500, detail=f"Task processing failed: {str(e)}")


@app.post("/dlq-handler")
async def handle_dlq_task(request: Request):
    """Handle tasks from Dead Letter Queue - store in Dapr for manual review"""
    try:
        body = await request.body()
        logger.warning(f"Received DLQ message: {body.decode()}")

        data = json.loads(body)
        task_data = data.get("data", data)
        task_id = task_data.get("task_id", "unknown")

        # Store DLQ task in Dapr state store for manual review
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")

        dlq_data = {
            "task_id": task_id,
            "original_data": task_data,
            "failure_reason": "Failed after maximum retries",
            "created_at": datetime.utcnow().isoformat(),
            "requires_manual_review": True,
        }

        state_data = [{"key": f"dlq-{task_id}", "value": dlq_data}]
        save_url = f"http://localhost:{dapr_port}/v1.0/state/statestore"

        response = await call_dapr_with_retry("POST", save_url, json_data=state_data)

        if response and response.status_code in [200, 201, 204]:
            logger.warning(f"DLQ task stored in Dapr for manual review: {task_id}")
        else:
            logger.error(f"Failed to store DLQ task in Dapr: {task_id}")

        logger.info(f"📥 DLQ task {task_id} stored for manual review")
        return  # HTTP 200 with empty body for Dapr pub/sub success

    except Exception as e:
        logger.error(f"Error handling DLQ task: {e}")
        raise HTTPException(status_code=500, detail=f"DLQ handling failed: {str(e)}")


# ============================================================================
# MINIMAL ENDPOINTS (ONLY HEALTH CHECK)
# ============================================================================


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check Dapr connectivity
    dapr_status = "unknown"
    try:
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
        health_url = f"http://localhost:{dapr_port}/v1.0/healthz"

        async with httpx.AsyncClient(timeout=2.0) as client:
            response = await client.get(health_url)
            dapr_status = (
                "healthy"
                if response.status_code in [200, 204]
                else f"unhealthy ({response.status_code})"
            )
    except Exception as e:
        dapr_status = f"unreachable ({str(e)[:50]})"

    return {
        "status": "healthy",
        "service": "herbit-worker",
        "dapr_status": dapr_status,
        "timestamp": datetime.utcnow().isoformat(),
    }


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("PORT", "8001"))
    reload_mode = os.getenv("WORKER_RELOAD", "false").lower() == "true"
    uvicorn.run("worker:app", host="0.0.0.0", port=port, reload=reload_mode)
